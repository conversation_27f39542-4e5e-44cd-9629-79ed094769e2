import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { useMemoryMonitor } from '@/utils/memory-cleanup';
import Colors from '@/constants/colors';

// Fallback component for lazy loading
const ChatLoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.light.primary} />
  </View>
);

export default function AssistantScreen() {
  const insets = useSafeAreaInsets();

  // Monitor memory usage
  useMemoryMonitor('AssistantScreen');

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Suspense fallback={<ChatLoadingFallback />}>
        <LazyComponents.ChatGPTInterface />
      </Suspense>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
});
