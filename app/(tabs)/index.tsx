import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { useMemoryMonitor } from '@/utils/memory-cleanup';
import Colors from '@/constants/colors';

export default function AssistantScreen() {
  const insets = useSafeAreaInsets();

  // Monitor memory usage
  useMemoryMonitor('AssistantScreen');

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ChatGPTInterface />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
