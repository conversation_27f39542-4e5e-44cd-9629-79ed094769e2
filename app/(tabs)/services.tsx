import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import {
  Zap,
  Users,
  Package,
  BarChart3,
  Sparkles,
  TrendingUp,
  Shield,
  X,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { typography, spacing, radius } from '@/constants/theme';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import InventoryReports from '@/components/reports/InventoryReports';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useAnimationsEnabled, useHapticsEnabled, useWhimsyStore } from '@/stores/whimsy-store';

// Animated components
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedView = Animated.createAnimatedComponent(View);

// Animated logo with easter egg
const AnimatedLogo: React.FC = () => {
  const { incrementEasterEggCount, easterEggCount } = useWhimsyStore();
  const animationsEnabled = useAnimationsEnabled();
  const hapticsEnabled = useHapticsEnabled();

  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${rotation.value}deg` },
      { scale: scale.value },
    ],
  }));

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    incrementEasterEggCount();

    if (animationsEnabled) {
      // Rotation animation
      rotation.value = withSequence(
        withTiming(15, { duration: 100 }),
        withTiming(-15, { duration: 100 }),
        withTiming(0, { duration: 100 })
      );

      // Scale animation
      scale.value = withSequence(
        withTiming(1.1, { duration: 100 }),
        withTiming(1, { duration: 100 })
      );
    }

    // Easter egg at 10 taps
    if (easterEggCount === 9) {
      Alert.alert(
        '🎉 ¡Easter Egg!',
        '¡Has descubierto el secreto de Salonier! Eres un verdadero explorador.',
        [{ text: '¡Genial!', style: 'default' }]
      );
    }
  };

  return (
    <AnimatedTouchableOpacity onPress={handlePress} style={animatedStyle}>
      <Sparkles size={32} color={Colors.light.primary} />
    </AnimatedTouchableOpacity>
  );
};

// Breathing card component
const BreathingCard: React.FC<{
  children: React.ReactNode;
  onPress?: () => void;
  delay?: number;
}> = ({ children, onPress, delay = 0 }) => {
  const animationsEnabled = useAnimationsEnabled();
  const hapticsEnabled = useHapticsEnabled();
  const scale = useSharedValue(1);

  useEffect(() => {
    if (animationsEnabled) {
      scale.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1.02, { duration: 2000 }),
            withTiming(1, { duration: 2000 })
          ),
          -1,
          true
        )
      );
    }
  }, [animationsEnabled, delay]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    onPress?.();
  };

  return (
    <AnimatedTouchableOpacity
      style={animatedStyle}
      onPress={handlePress}
      activeOpacity={0.95}
    >
      {children}
    </AnimatedTouchableOpacity>
  );
};

// Metric card component
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<any>;
  color: string;
  onPress?: () => void;
}> = ({ title, value, subtitle, icon: Icon, color, onPress }) => {
  const animationsEnabled = useAnimationsEnabled();
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);

  useEffect(() => {
    if (animationsEnabled) {
      opacity.value = withTiming(1, { duration: 600 });
      translateY.value = withTiming(0, { duration: 600 });
    } else {
      opacity.value = 1;
      translateY.value = 0;
    }
  }, [animationsEnabled]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <AnimatedView style={[styles.metricCard, animatedStyle]}>
      <TouchableOpacity
        style={styles.metricCardContent}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={[styles.metricIcon, { backgroundColor: color + '15' }]}>
          <Icon size={24} color={color} />
        </View>
        <View style={styles.metricInfo}>
          <Text style={styles.metricValue}>{value}</Text>
          <Text style={styles.metricTitle}>{title}</Text>
          {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
        </View>
      </TouchableOpacity>
    </AnimatedView>
  );
};

export default function ServicesScreen() {
  const [showReportsModal, setShowReportsModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { configuration } = useSalonConfigStore();
  const { getPendingDrafts, deleteDraft } = useServiceDraftStore();
  const { metrics, isLoading, loadTodayMetrics, startAutoRefresh } = useDashboardStore();

  const handleProductSelect = (productId: string) => {
    setShowReportsModal(false);
    router.push(`/inventory/${productId}`);
  };

  // Check for pending service drafts on mount
  useEffect(() => {
    const checkPendingDrafts = async () => {
      const drafts = getPendingDrafts();
      if (drafts.length > 0) {
        const draftNames = drafts.map(d => d.client?.name || 'Cliente sin nombre').join(', ');
        Alert.alert(
          'Servicios Pendientes',
          `Tienes ${drafts.length} servicio(s) sin completar: ${draftNames}. ¿Quieres continuar?`,
          [
            {
              text: 'Eliminar',
              style: 'destructive',
              onPress: () => {
                drafts.forEach(draft => deleteDraft(draft.id));
              },
            },
            {
              text: 'Continuar',
              onPress: () => {
                router.push('/service/new');
              },
            },
            { text: 'Cancelar', style: 'cancel' },
          ]
        );
      }
    };

    checkPendingDrafts();
  }, []);

  // Load metrics and start auto-refresh
  useEffect(() => {
    loadTodayMetrics();
    const cleanup = startAutoRefresh();
    return cleanup;
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadTodayMetrics();
    setIsRefreshing(false);
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={Colors.light.primary}
        />
      }
    >
      {/* Header with animated logo */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.welcomeText}>¡Hola!</Text>
            <Text style={styles.salonName}>
              {configuration.businessName || 'Mi Salón'}
            </Text>
          </View>
          <AnimatedLogo />
        </View>
      </View>

      {/* Today's metrics */}
      <View style={styles.metricsSection}>
        <Text style={styles.sectionTitle}>Hoy</Text>
        
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.light.primary} />
          </View>
        ) : (
          <View style={styles.metricsGrid}>
            <MetricCard
              title="Servicios"
              value={metrics.servicesCount}
              subtitle="completados"
              icon={Zap}
              color={Colors.light.primary}
              onPress={() => router.push('/clients')}
            />
            <MetricCard
              title="Ingresos"
              value={`€${metrics.totalRevenue.toFixed(0)}`}
              subtitle="generados"
              icon={TrendingUp}
              color={Colors.light.success}
              onPress={() => setShowReportsModal(true)}
            />
            <MetricCard
              title="Clientes"
              value={metrics.uniqueClients}
              subtitle="atendidos"
              icon={Users}
              color={Colors.light.secondary}
              onPress={() => router.push('/clients')}
            />
            <MetricCard
              title="Productos"
              value={metrics.productsUsed}
              subtitle="utilizados"
              icon={Package}
              color={Colors.light.accent}
              onPress={() => router.push('/inventory')}
            />
          </View>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Acciones Rápidas</Text>

        <BreathingCard onPress={() => router.push('/service/client-selection')}>
          <LinearGradient
            colors={[Colors.light.primary, Colors.light.secondary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.primaryAction}
          >
            <View style={styles.actionIcon}>
              <Zap size={28} color="white" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Nuevo Servicio</Text>
              <Text style={styles.actionSubtitle}>
                Diagnóstico capilar inteligente con selección de cliente
              </Text>
            </View>
            <Sparkles size={24} color="white" />
          </LinearGradient>
        </BreathingCard>

        <View style={styles.secondaryActions}>
          <BreathingCard onPress={() => router.push('/clients')} delay={100}>
            <View style={styles.secondaryAction}>
              <Users size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Clientes</Text>
            </View>
          </BreathingCard>

          <BreathingCard onPress={() => router.push('/inventory')} delay={200}>
            <View style={styles.secondaryAction}>
              <Package size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Inventario</Text>
            </View>
          </BreathingCard>

          <BreathingCard onPress={() => setShowReportsModal(true)} delay={300}>
            <View style={styles.secondaryAction}>
              <BarChart3 size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Reportes</Text>
            </View>
          </BreathingCard>
        </View>
      </View>

      {/* Reports Modal */}
      <Modal
        visible={showReportsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowReportsModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <TouchableOpacity
              onPress={() => setShowReportsModal(false)}
              style={styles.closeButton}
            >
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>
          <InventoryReports onProductSelect={handleProductSelect} />
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  contentContainer: {
    paddingBottom: spacing.xl,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  salonName: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginTop: 2,
  },
  metricsSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  loadingContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  metricCard: {
    width: '48%',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...commonStyles.shadow,
  },
  metricCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  metricInfo: {
    flex: 1,
  },
  metricValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  metricTitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  metricSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  quickActions: {
    paddingHorizontal: spacing.lg,
  },
  primaryAction: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: radius.xl,
    marginBottom: spacing.md,
    ...commonStyles.shadow,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: radius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: 'white',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18,
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  secondaryAction: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    padding: spacing.lg,
    borderRadius: radius.lg,
    alignItems: 'center',
    ...commonStyles.shadow,
  },
  secondaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
});
