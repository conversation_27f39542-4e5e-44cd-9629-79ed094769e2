import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { Search, Plus, ChevronRight, User, Phone, Calendar } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClientStore, Client } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { BaseHeader } from '@/components/base/BaseHeader';

export default function ClientSelectionScreen() {
  console.log('ClientSelectionScreen: Iniciando componente...');

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const { clients, isLoading, loadClients } = useClientStore();
  const { initializeClientProfile } = useClientHistoryStore();
  const { skipSafetyVerification } = useSalonConfigStore();

  console.log('ClientSelectionScreen: Stores cargados correctamente');

  // Load clients on mount
  useEffect(() => {
    loadClients();
  }, [loadClients]);

  // Initialize client profiles when clients are loaded
  useEffect(() => {
    if (clients.length > 0) {
      clients.forEach(client => {
        initializeClientProfile(client.id);
      });
    }
  }, [clients, initializeClientProfile]);

  const getFilteredClients = () => {
    if (!searchQuery.trim()) return clients;

    return clients.filter(
      client =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.email.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const filteredClients = getFilteredClients();

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
  };

  const handleContinue = () => {
    if (selectedClient) {
      if (skipSafetyVerification) {
        // Saltar directamente al diagnóstico
        router.push(`/service/new?clientId=${selectedClient.id}`);
      } else {
        router.push(`/service/safety-verification?clientId=${selectedClient.id}`);
      }
    }
  };

  const handleNewClient = () => {
    router.push('/client/new');
  };

  const handleGuestAnalysis = () => {
    if (skipSafetyVerification) {
      // Saltar directamente al diagnóstico sin cliente
      router.push('/service/new');
    } else {
      router.push('/service/safety-verification');
    }
  };

  const renderClientCard = ({ item }: { item: Client }) => {
    const isSelected = selectedClient?.id === item.id;

    return (
      <TouchableOpacity
        style={[styles.clientCard, isSelected && styles.selectedClientCard]}
        onPress={() => handleClientSelect(item)}
      >
        <View style={styles.clientAvatar}>
          <Text style={styles.clientInitial}>{item.name.charAt(0)}</Text>
        </View>

        <View style={styles.clientInfo}>
          <Text style={styles.clientName}>{item.name}</Text>
          <View style={styles.clientDetails}>
            <View style={styles.detailRow}>
              <Phone size={14} color={Colors.light.gray} />
              <Text style={styles.clientPhone}>{item.phone}</Text>
            </View>
            <View style={styles.detailRow}>
              <Calendar size={14} color={Colors.light.gray} />
              <Text style={styles.clientLastVisit}>Última visita: {item.lastVisit}</Text>
            </View>
          </View>
        </View>

        <View style={styles.selectIndicator}>
          {isSelected && (
            <View style={styles.selectedIndicator}>
              <Text style={styles.selectedText}>✓</Text>
            </View>
          )}
          <ChevronRight size={20} color={Colors.light.gray} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <BaseHeader
        title="Cliente"
        subtitle="Selecciona o crea un nuevo cliente"
        onBack={() => router.back()}
      />

      <View style={styles.progressIndicator}>
        <View style={styles.progressDot} />
        <View style={[styles.progressDot, styles.inactiveDot]} />
        <View style={[styles.progressDot, styles.inactiveDot]} />
        <View style={[styles.progressDot, styles.inactiveDot]} />
        <View style={[styles.progressDot, styles.inactiveDot]} />
      </View>

      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por nombre o teléfono..."
            placeholderTextColor={Colors.light.gray}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <TouchableOpacity style={styles.addClientButton} onPress={handleNewClient}>
          <Plus size={20} color={Colors.light.primary} />
          <Text style={styles.addClientText}>Agregar nuevo cliente</Text>
        </TouchableOpacity>

        <View style={styles.clientsList}>
          <Text style={styles.sectionTitle}>
            {searchQuery
              ? `${filteredClients.length} resultado${filteredClients.length !== 1 ? 's' : ''}`
              : 'Clientes recientes'}
          </Text>

          <FlatList
            data={filteredClients}
            keyExtractor={item => item.id}
            renderItem={renderClientCard}
            ListEmptyComponent={
              isLoading ? (
                <View style={styles.loadingState}>
                  <ActivityIndicator size="large" color={Colors.light.primary} />
                  <Text style={styles.loadingText}>Cargando clientes...</Text>
                </View>
              ) : (
                <View style={styles.emptyState}>
                  <User size={48} color={Colors.light.gray} />
                  <Text style={styles.emptyStateTitle}>
                    {searchQuery ? 'No se encontraron clientes' : 'No hay clientes'}
                  </Text>
                  <Text style={styles.emptyStateText}>
                    {searchQuery
                      ? 'Intenta con otro término de búsqueda o agrega un nuevo cliente'
                      : 'Comienza añadiendo tu primer cliente'}
                  </Text>
                  <TouchableOpacity style={styles.emptyStateButton} onPress={handleNewClient}>
                    <Plus size={20} color={Colors.light.textLight} />
                    <Text style={styles.emptyStateButtonText}>Añadir Cliente</Text>
                  </TouchableOpacity>
                </View>
              )
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        </View>

        <View style={styles.bottomActions}>
          <TouchableOpacity style={styles.guestButton} onPress={handleGuestAnalysis}>
            <Text style={styles.guestButtonText}>Análisis sin cliente</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.continueButton, !selectedClient && styles.continueButtonDisabled]}
            onPress={handleContinue}
            disabled={!selectedClient}
          >
            <Text
              style={[
                styles.continueButtonText,
                !selectedClient && styles.continueButtonTextDisabled,
              ]}
            >
              Siguiente
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  progressIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.text,
  },
  inactiveDot: {
    backgroundColor: Colors.light.lightGray,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  addClientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 24,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
    gap: 8,
  },
  addClientText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  clientsList: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  listContainer: {
    flexGrow: 1,
  },
  clientCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
  },
  selectedClientCard: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '05',
  },
  clientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  clientInitial: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
  },
  clientDetails: {
    gap: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  clientPhone: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  clientLastVisit: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  selectIndicator: {
    alignItems: 'center',
    gap: 8,
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedText: {
    color: Colors.light.textLight,
    fontSize: 14,
    fontWeight: '700',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    gap: 8,
  },
  emptyStateButtonText: {
    color: Colors.light.textLight,
    fontWeight: '700',
    fontSize: 16,
  },
  bottomActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  guestButton: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  guestButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
    fontSize: 16,
  },
  continueButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  continueButtonDisabled: {
    backgroundColor: Colors.light.lightGray,
    shadowOpacity: 0,
    elevation: 0,
  },
  continueButtonText: {
    color: Colors.light.textLight,
    fontWeight: '700',
    fontSize: 16,
  },
  continueButtonTextDisabled: {
    color: Colors.light.gray,
  },
  loadingState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.light.gray,
    marginTop: 16,
  },
});
