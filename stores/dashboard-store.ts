import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { logger } from '@/utils/logger';

export interface DashboardMetrics {
  servicestoday: number;
  uniqueClientsToday: number;
  averageSatisfaction: number;
  totalClients: number;
  totalRevenue: number;
  lastUpdated: Date | null;
}

interface DashboardStore {
  // State
  metrics: DashboardMetrics;
  isLoading: boolean;
  error: Error | null;
  lastFetch: Date | null;

  // Actions
  loadTodayMetrics: () => Promise<void>;
  refreshMetrics: () => Promise<void>;
  clearCache: () => void;
  startAutoRefresh: () => () => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const AUTO_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes

export const useDashboardStore = create<DashboardStore>()(
  persist(
    (set, get) => ({
      // Initial state
      metrics: {
        servicestoday: 0,
        uniqueClientsToday: 0,
        averageSatisfaction: 0,
        totalClients: 0,
        totalRevenue: 0,
        lastUpdated: null,
      },
      isLoading: false,
      error: null,
      lastFetch: null,

      // Load today's metrics from Supabase
      loadTodayMetrics: async () => {
        const state = get();

        // Check cache validity
        if (state.lastFetch) {
          const timeSinceLastFetch = Date.now() - state.lastFetch.getTime();
          if (timeSinceLastFetch < CACHE_DURATION) {
            logger.info('Dashboard metrics loaded from cache');
            return;
          }
        }

        set({ isLoading: true, error: null });

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            throw new Error('No salon ID available');
          }

          // Get today's date in the database timezone
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);

          // Parallel queries for better performance
          const [servicesResult, clientsResult, satisfactionResult, totalClientsResult] =
            await Promise.all([
              // Services completed today
              supabase
                .from('services')
                .select('id', { count: 'exact', head: true })
                .eq('salon_id', salonId)
                .eq('status', 'completed')
                .gte('service_date', today.toISOString())
                .lt('service_date', tomorrow.toISOString()),

              // Unique clients served today
              supabase
                .from('services')
                .select('client_id')
                .eq('salon_id', salonId)
                .eq('status', 'completed')
                .gte('service_date', today.toISOString())
                .lt('service_date', tomorrow.toISOString()),

              // Average satisfaction today
              supabase
                .from('services')
                .select('satisfaction_score')
                .eq('salon_id', salonId)
                .eq('status', 'completed')
                .gte('service_date', today.toISOString())
                .lt('service_date', tomorrow.toISOString())
                .not('satisfaction_score', 'is', null),

              // Total clients
              supabase
                .from('clients')
                .select('id', { count: 'exact', head: true })
                .eq('salon_id', salonId),
            ]);

          if (servicesResult.error) throw servicesResult.error;
          if (clientsResult.error) throw clientsResult.error;
          if (satisfactionResult.error) throw satisfactionResult.error;
          if (totalClientsResult.error) throw totalClientsResult.error;

          // Calculate unique clients
          const uniqueClients = clientsResult.data
            ? new Set(clientsResult.data.map(s => s.client_id)).size
            : 0;

          // Calculate average satisfaction
          let avgSatisfaction = 0;
          if (satisfactionResult.data && satisfactionResult.data.length > 0) {
            const sum = satisfactionResult.data.reduce(
              (acc, s) => acc + (s.satisfaction_score || 0),
              0
            );
            avgSatisfaction = sum / satisfactionResult.data.length;
          }

          const newMetrics: DashboardMetrics = {
            servicestoday: servicesResult.count || 0,
            uniqueClientsToday: uniqueClients,
            averageSatisfaction: avgSatisfaction,
            totalClients: totalClientsResult.count || 0,
            totalRevenue: 0, // TODO: Implement revenue calculation
            lastUpdated: new Date(),
          };

          set({
            metrics: newMetrics,
            isLoading: false,
            lastFetch: new Date(),
          });

          logger.info('Dashboard metrics loaded successfully', newMetrics);
        } catch (error) {
          logger.error('Failed to load dashboard metrics', error);
          set({
            error: error instanceof Error ? error : new Error('Unknown error'),
            isLoading: false,
          });
        }
      },

      // Force refresh metrics (ignores cache)
      refreshMetrics: async () => {
        set({ lastFetch: null });
        await get().loadTodayMetrics();
      },

      // Clear cache
      clearCache: () => {
        set({
          metrics: {
            servicestoday: 0,
            uniqueClientsToday: 0,
            averageSatisfaction: 0,
            totalClients: 0,
            totalRevenue: 0,
            lastUpdated: null,
          },
          lastFetch: null,
          error: null,
        });
      },

      // Force reset corrupted state
      forceReset: () => {
        // Clear persisted state
        if (typeof window !== 'undefined' && window.localStorage) {
          try {
            window.localStorage.removeItem('dashboard-store');
          } catch (error) {
            console.warn('Could not clear dashboard store from localStorage:', error);
          }
        }

        // Reset to initial state
        set({
          metrics: {
            servicestoday: 0,
            uniqueClientsToday: 0,
            averageSatisfaction: 0,
            totalClients: 0,
            totalRevenue: 0,
            lastUpdated: null,
          },
          lastFetch: null,
          error: null,
          isLoading: false,
        });
      },

      // Start auto-refresh timer
      startAutoRefresh: () => {
        const intervalId = setInterval(() => {
          get().loadTodayMetrics();
        }, AUTO_REFRESH_INTERVAL);

        // Return cleanup function
        return () => clearInterval(intervalId);
      },
    }),
    {
      name: 'dashboard-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        metrics: state.metrics,
        lastFetch: state.lastFetch,
      }),
    }
  )
);
