import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withSequence,
  withTiming,
  withDelay,
  interpolate,
} from 'react-native-reanimated';
import { <PERSON>, <PERSON>rkles, Zap } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface EnhancedTypingIndicatorProps {
  visible: boolean;
  status?: 'thinking' | 'analyzing' | 'writing' | 'idle';
  message?: string;
}

const AnimatedView = Animated.createAnimatedComponent(View);

export default function EnhancedTypingIndicator({
  visible,
  status = 'thinking',
  message,
}: EnhancedTypingIndicatorProps) {
  // Animation values
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);
  const iconRotation = useSharedValue(0);
  const iconScale = useSharedValue(1);

  useEffect(() => {
    if (visible) {
      // Container animations
      opacity.value = withTiming(1, { duration: 300 });
      scale.value = withTiming(1, { duration: 400 });

      // Dot animations with stagger
      const dotAnimation = withRepeat(
        withSequence(
          withTiming(1, { duration: 400 }),
          withTiming(0.3, { duration: 400 })
        ),
        -1,
        false
      );

      dot1.value = dotAnimation;
      dot2.value = withDelay(200, dotAnimation);
      dot3.value = withDelay(400, dotAnimation);

      // Icon animations based on status
      if (status === 'analyzing') {
        iconRotation.value = withRepeat(
          withTiming(360, { duration: 2000 }),
          -1,
          false
        );
      } else if (status === 'thinking') {
        iconScale.value = withRepeat(
          withSequence(
            withTiming(1.2, { duration: 800 }),
            withTiming(1, { duration: 800 })
          ),
          -1,
          true
        );
      }
    } else {
      opacity.value = withTiming(0, { duration: 200 });
      scale.value = withTiming(0.8, { duration: 200 });
    }
  }, [visible, status]);

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const dot1AnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(dot1.value, [0, 1], [0.3, 1]),
    transform: [{ scale: interpolate(dot1.value, [0, 1], [0.8, 1.2]) }],
  }));

  const dot2AnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(dot2.value, [0, 1], [0.3, 1]),
    transform: [{ scale: interpolate(dot2.value, [0, 1], [0.8, 1.2]) }],
  }));

  const dot3AnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(dot3.value, [0, 1], [0.3, 1]),
    transform: [{ scale: interpolate(dot3.value, [0, 1], [0.8, 1.2]) }],
  }));

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${iconRotation.value}deg` },
      { scale: iconScale.value },
    ],
  }));

  const getStatusIcon = () => {
    switch (status) {
      case 'thinking':
        return <Brain size={16} color={Colors.light.primary} />;
      case 'analyzing':
        return <Zap size={16} color={Colors.light.secondary} />;
      case 'writing':
        return <Sparkles size={16} color={Colors.light.accent} />;
      default:
        return <Brain size={16} color={Colors.light.primary} />;
    }
  };

  const getStatusMessage = () => {
    if (message) return message;
    
    switch (status) {
      case 'thinking':
        return 'Pensando...';
      case 'analyzing':
        return 'Analizando imagen...';
      case 'writing':
        return 'Escribiendo respuesta...';
      default:
        return 'Procesando...';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'thinking':
        return Colors.light.primary;
      case 'analyzing':
        return Colors.light.secondary;
      case 'writing':
        return Colors.light.accent;
      default:
        return Colors.light.primary;
    }
  };

  if (!visible) return null;

  return (
    <AnimatedView style={[styles.container, containerAnimatedStyle]}>
      <View style={styles.content}>
        {/* Avatar with animated icon */}
        <View style={styles.avatar}>
          <AnimatedView style={iconAnimatedStyle}>
            {getStatusIcon()}
          </AnimatedView>
        </View>

        {/* Message bubble */}
        <View style={[styles.bubble, { borderColor: getStatusColor() + '20' }]}>
          <View style={styles.messageContainer}>
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusMessage()}
            </Text>
            
            {/* Animated dots */}
            <View style={styles.dotsContainer}>
              <AnimatedView style={[styles.dot, dot1AnimatedStyle, { backgroundColor: getStatusColor() }]} />
              <AnimatedView style={[styles.dot, dot2AnimatedStyle, { backgroundColor: getStatusColor() }]} />
              <AnimatedView style={[styles.dot, dot3AnimatedStyle, { backgroundColor: getStatusColor() }]} />
            </View>
          </View>
        </View>
      </View>
    </AnimatedView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    marginTop: spacing.xs,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bubble: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderBottomLeftRadius: radius.sm,
    padding: spacing.md,
    maxWidth: '80%',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    marginRight: spacing.sm,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
});
