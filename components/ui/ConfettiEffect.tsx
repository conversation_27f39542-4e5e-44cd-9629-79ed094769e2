import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withDelay,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import Colors from '@/constants/colors';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ConfettiPiece {
  id: number;
  color: string;
  size: number;
  startX: number;
  delay: number;
}

interface ConfettiEffectProps {
  visible: boolean;
  onComplete?: () => void;
  intensity?: 'low' | 'medium' | 'high';
  colors?: string[];
}

const DEFAULT_COLORS = [
  Colors.light.primary,
  Colors.light.secondary,
  Colors.light.accent,
  Colors.light.success,
  Colors.light.warning,
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
  '#96CEB4',
  '#FFEAA7',
];

const AnimatedView = Animated.createAnimatedComponent(View);

export default function ConfettiEffect({
  visible,
  onComplete,
  intensity = 'medium',
  colors = DEFAULT_COLORS,
}: ConfettiEffectProps) {
  const opacity = useSharedValue(0);

  // Generate confetti pieces based on intensity
  const getConfettiCount = () => {
    switch (intensity) {
      case 'low': return 15;
      case 'medium': return 30;
      case 'high': return 50;
      default: return 30;
    }
  };

  const confettiPieces: ConfettiPiece[] = Array.from({ length: getConfettiCount() }, (_, i) => ({
    id: i,
    color: colors[Math.floor(Math.random() * colors.length)],
    size: Math.random() * 8 + 4,
    startX: Math.random() * SCREEN_WIDTH,
    delay: Math.random() * 1000,
  }));

  useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 300 });
      
      // Auto-hide after animation
      const timer = setTimeout(() => {
        opacity.value = withTiming(0, { duration: 500 });
        setTimeout(() => {
          onComplete?.();
        }, 500);
      }, 3000);

      return () => clearTimeout(timer);
    } else {
      opacity.value = withTiming(0, { duration: 300 });
    }
  }, [visible]);

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  if (!visible) return null;

  return (
    <AnimatedView style={[styles.container, containerAnimatedStyle]} pointerEvents="none">
      {confettiPieces.map((piece) => (
        <ConfettiPiece key={piece.id} piece={piece} />
      ))}
    </AnimatedView>
  );
}

interface ConfettiPieceProps {
  piece: ConfettiPiece;
}

function ConfettiPiece({ piece }: ConfettiPieceProps) {
  const translateY = useSharedValue(-50);
  const translateX = useSharedValue(0);
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);

  useEffect(() => {
    // Fall animation
    translateY.value = withDelay(
      piece.delay,
      withTiming(SCREEN_HEIGHT + 100, {
        duration: 3000 + Math.random() * 2000,
        easing: Easing.out(Easing.quad),
      })
    );

    // Horizontal drift
    translateX.value = withDelay(
      piece.delay,
      withTiming((Math.random() - 0.5) * 200, {
        duration: 3000 + Math.random() * 2000,
        easing: Easing.inOut(Easing.sine),
      })
    );

    // Rotation
    rotation.value = withDelay(
      piece.delay,
      withRepeat(
        withTiming(360, {
          duration: 1000 + Math.random() * 1000,
          easing: Easing.linear,
        }),
        -1,
        false
      )
    );

    // Scale pulsing
    scale.value = withDelay(
      piece.delay,
      withRepeat(
        withSequence(
          withTiming(1.2, { duration: 500 }),
          withTiming(0.8, { duration: 500 })
        ),
        -1,
        true
      )
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: piece.startX + translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotation.value}deg` },
      { scale: scale.value },
    ],
  }));

  return (
    <AnimatedView
      style={[
        styles.confettiPiece,
        {
          backgroundColor: piece.color,
          width: piece.size,
          height: piece.size,
        },
        animatedStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  confettiPiece: {
    position: 'absolute',
    borderRadius: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
});
