import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
} from 'react-native';
import { Terminal, Zap, Users, Package, BarChart3, Beaker, HelpCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface CommandSuggestion {
  command: string;
  description: string;
  icon: React.ComponentType<any>;
  example?: string;
}

interface CommandSuggestionsProps {
  input: string;
  onSuggestionSelect: (command: string) => void;
}

const COMMAND_SUGGESTIONS: CommandSuggestion[] = [
  {
    command: '/nuevo-servicio',
    description: 'Iniciar un nuevo servicio de coloración',
    icon: Zap,
    example: '/nuevo-servicio <PERSON>'
  },
  {
    command: '/cliente',
    description: 'Buscar información de un cliente',
    icon: Users,
    example: '/cliente Ana'
  },
  {
    command: '/inventario',
    description: 'Consultar productos en stock',
    icon: Package,
    example: '/inventario L\'Oréal'
  },
  {
    command: '/reportes',
    description: 'Ver reportes y estadísticas',
    icon: BarChart3,
    example: '/reportes ventas'
  },
  {
    command: '/formula',
    description: 'Crear una fórmula personalizada',
    icon: Beaker,
    example: '/formula rubio ceniza'
  },
  {
    command: '/ayuda',
    description: 'Mostrar todos los comandos disponibles',
    icon: HelpCircle,
  }
];

export default function CommandSuggestions({ input, onSuggestionSelect }: CommandSuggestionsProps) {
  // Solo mostrar si el input empieza con "/"
  if (!input.startsWith('/')) {
    return null;
  }

  // Filtrar comandos que coincidan con el input
  const filteredCommands = COMMAND_SUGGESTIONS.filter(cmd =>
    cmd.command.toLowerCase().startsWith(input.toLowerCase())
  );

  if (filteredCommands.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Terminal size={16} color={Colors.light.primary} />
        <Text style={styles.headerText}>Comandos disponibles</Text>
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.suggestionsContainer}
      >
        {filteredCommands.map((suggestion, index) => (
          <TouchableOpacity
            key={suggestion.command}
            style={styles.suggestionCard}
            onPress={() => onSuggestionSelect(suggestion.command)}
            activeOpacity={0.7}
          >
            <View style={styles.suggestionHeader}>
              <View style={styles.iconContainer}>
                <suggestion.icon size={18} color={Colors.light.primary} />
              </View>
              <Text style={styles.commandText}>{suggestion.command}</Text>
            </View>
            
            <Text style={styles.descriptionText} numberOfLines={2}>
              {suggestion.description}
            </Text>
            
            {suggestion.example && (
              <Text style={styles.exampleText} numberOfLines={1}>
                Ej: {suggestion.example}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
    paddingBottom: spacing.xs,
  },
  headerText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginLeft: spacing.xs,
  },
  suggestionsContainer: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.sm,
    gap: spacing.sm,
  },
  suggestionCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.sm,
    padding: spacing.sm,
    width: 200,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: radius.xs,
    backgroundColor: Colors.light.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.xs,
  },
  commandText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  descriptionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    lineHeight: 16,
    marginBottom: spacing.xs,
  },
  exampleText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
});
