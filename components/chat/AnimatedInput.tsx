import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import { Send, Camera, Image as ImageIcon } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface AnimatedInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onImageCapture: () => void;
  onImagePicker: () => void;
  placeholder?: string;
  isSending?: boolean;
  disabled?: boolean;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export default function AnimatedInput({
  value,
  onChangeText,
  onSend,
  onImageCapture,
  onImagePicker,
  placeholder = 'Escribe tu mensaje...',
  isSending = false,
  disabled = false,
}: AnimatedInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Animation values
  const focusAnimation = useSharedValue(0);
  const sendButtonScale = useSharedValue(1);
  const borderAnimation = useSharedValue(0);

  const canSend = value.trim().length > 0 && !isSending && !disabled;

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    focusAnimation.value = withSpring(1, { damping: 15, stiffness: 150 });
    borderAnimation.value = withTiming(1, { duration: 200 });
  };

  const handleBlur = () => {
    setIsFocused(false);
    focusAnimation.value = withSpring(0, { damping: 15, stiffness: 150 });
    borderAnimation.value = withTiming(0, { duration: 200 });
  };

  // Handle send button press
  const handleSendPress = () => {
    if (!canSend) return;
    
    sendButtonScale.value = withSpring(0.9, { damping: 10, stiffness: 200 }, () => {
      sendButtonScale.value = withSpring(1, { damping: 10, stiffness: 200 });
    });
    
    onSend();
  };

  // Animated styles
  const containerAnimatedStyle = useAnimatedStyle(() => ({
    borderColor: interpolateColor(
      borderAnimation.value,
      [0, 1],
      [Colors.light.border + '60', Colors.light.primary + '80']
    ),
    shadowOpacity: 0.05 + (focusAnimation.value * 0.1),
    transform: [{ scale: 1 + (focusAnimation.value * 0.005) }],
  }));

  const sendButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sendButtonScale.value }],
    backgroundColor: canSend ? Colors.light.primary : Colors.light.border + '80',
  }));

  const attachButtonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: isFocused ? 0.6 : 1,
    transform: [{ scale: isFocused ? 0.9 : 1 }],
  }));

  return (
    <Animated.View style={[styles.container, containerAnimatedStyle]}>
      {/* Attach buttons */}
      <Animated.View style={[styles.attachButtonsContainer, attachButtonAnimatedStyle]}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={onImageCapture}
          disabled={disabled}
          activeOpacity={0.7}
        >
          <Camera size={20} color={Colors.light.textSecondary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.attachButton}
          onPress={onImagePicker}
          disabled={disabled}
          activeOpacity={0.7}
        >
          <ImageIcon size={20} color={Colors.light.textSecondary} />
        </TouchableOpacity>
      </Animated.View>

      {/* Text input */}
      <TextInput
        ref={inputRef}
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        placeholderTextColor={Colors.light.textSecondary + '80'}
        multiline
        editable={!disabled}
        maxLength={2000}
        returnKeyType="send"
        onSubmitEditing={canSend ? handleSendPress : undefined}
        blurOnSubmit={false}
      />

      {/* Send button */}
      <AnimatedTouchableOpacity
        style={[styles.sendButton, sendButtonAnimatedStyle]}
        onPress={handleSendPress}
        disabled={!canSend}
        activeOpacity={0.8}
      >
        {isSending ? (
          <ActivityIndicator size="small" color={Colors.light.surface} />
        ) : (
          <Send
            size={18}
            color={canSend ? Colors.light.surface : Colors.light.textSecondary}
          />
        )}
      </AnimatedTouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    borderWidth: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 56,
    maxHeight: 120,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 2,
  },
  attachButtonsContainer: {
    flexDirection: 'row',
    marginRight: spacing.sm,
  },
  attachButton: {
    padding: spacing.xs,
    marginRight: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: 'transparent',
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    maxHeight: 80,
    lineHeight: typography.sizes.base * 1.4,
    textAlignVertical: Platform.OS === 'android' ? 'top' : 'center',
    fontWeight: typography.weights.normal,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
