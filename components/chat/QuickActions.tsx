import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Zap,
  Users,
  Package,
  BarChart3,
  Camera,
  Beaker,
  Clock,
  Search,
} from 'lucide-react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { commonStyles } from '@/styles/commonStyles';
import { useEnhancedHaptics } from '@/hooks/useEnhancedHaptics';

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  color: string;
  action: () => void;
  type: 'primary' | 'secondary';
}

interface QuickActionsProps {
  onActionPress: (message: string) => void;
  onImageCapture: () => void;
  onCelebration?: () => void;
}

export default function QuickActions({ onActionPress, onImageCapture, onCelebration }: QuickActionsProps) {
  const haptics = useEnhancedHaptics();
  const quickActions: QuickAction[] = [
    {
      id: 'new-service',
      title: 'Nuevo Servicio',
      subtitle: 'Iniciar análisis capilar',
      icon: Zap,
      color: Colors.light.primary,
      type: 'primary',
      action: () => {
        haptics.success();
        onCelebration?.();
        onActionPress('Quiero iniciar un nuevo servicio de coloración');
      },
    },
    {
      id: 'photo-analysis',
      title: 'Analizar Foto',
      subtitle: 'Diagnóstico con IA',
      icon: Camera,
      color: Colors.light.secondary,
      type: 'primary',
      action: () => {
        haptics.medium();
        onImageCapture();
      },
    },
    {
      id: 'quick-formula',
      title: 'Fórmula Rápida',
      subtitle: 'Crear fórmula personalizada',
      icon: Beaker,
      color: Colors.light.accent,
      type: 'secondary',
      action: () => onActionPress('Necesito crear una fórmula para'),
    },
    {
      id: 'client-lookup',
      title: 'Buscar Cliente',
      subtitle: 'Historial y recomendaciones',
      icon: Users,
      color: Colors.light.info,
      type: 'secondary',
      action: () => onActionPress('Buscar información del cliente'),
    },
    {
      id: 'inventory-check',
      title: 'Consultar Stock',
      subtitle: 'Disponibilidad de productos',
      icon: Package,
      color: Colors.light.warning,
      type: 'secondary',
      action: () => onActionPress('¿Qué productos tengo disponibles?'),
    },
    {
      id: 'recent-services',
      title: 'Servicios Recientes',
      subtitle: 'Últimos trabajos realizados',
      icon: Clock,
      color: Colors.light.success,
      type: 'secondary',
      action: () => onActionPress('Muéstrame mis servicios recientes'),
    },
    {
      id: 'reports',
      title: 'Reportes',
      subtitle: 'Análisis y estadísticas',
      icon: BarChart3,
      color: Colors.light.purple || Colors.light.primary,
      type: 'secondary',
      action: () => router.push('/services'), // Navegar a la pantalla de servicios que tiene reportes
    },
    {
      id: 'search-technique',
      title: 'Buscar Técnica',
      subtitle: 'Métodos y procedimientos',
      icon: Search,
      color: Colors.light.teal || Colors.light.secondary,
      type: 'secondary',
      action: () => onActionPress('¿Qué técnica me recomiendas para'),
    },
  ];

  const primaryActions = quickActions.filter(action => action.type === 'primary');
  const secondaryActions = quickActions.filter(action => action.type === 'secondary');

  const renderAction = (action: QuickAction, isPrimary: boolean = false) => (
    <TouchableOpacity
      key={action.id}
      style={[
        styles.actionButton,
        isPrimary ? styles.primaryActionButton : styles.secondaryActionButton,
        { borderColor: action.color + '20' }
      ]}
      onPress={action.action}
      activeOpacity={0.7}
    >
      <View style={[
        styles.actionIcon,
        !isPrimary && styles.secondaryActionIcon,
        { backgroundColor: action.color + '15' }
      ]}>
        <action.icon size={isPrimary ? 24 : 20} color={action.color} />
      </View>
      <View style={styles.actionContent}>
        <Text style={[styles.actionTitle, isPrimary && styles.primaryActionTitle]}>
          {action.title}
        </Text>
        <Text style={[styles.actionSubtitle, isPrimary && styles.primaryActionSubtitle]}>
          {action.subtitle}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Primary Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Acciones Principales</Text>
        <View style={styles.primaryActionsContainer}>
          {primaryActions.map(action => renderAction(action, true))}
        </View>
      </View>

      {/* Secondary Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Más Opciones</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.secondaryActionsContainer}
        >
          {secondaryActions.map(action => renderAction(action, false))}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  primaryActionsContainer: {
    gap: spacing.md,
  },
  secondaryActionsContainer: {
    gap: spacing.sm,
    paddingRight: spacing.lg,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    ...commonStyles.shadow,
  },
  primaryActionButton: {
    padding: spacing.lg,
    minHeight: 80,
  },
  secondaryActionButton: {
    padding: spacing.md,
    width: 160,
    minHeight: 100,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  secondaryActionIcon: {
    width: 40,
    height: 40,
    marginRight: 0,
    marginBottom: spacing.sm,
  },
  actionIcon: {
    borderRadius: radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    width: 48,
    height: 48,
    marginRight: spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  primaryActionTitle: {
    fontSize: typography.sizes.lg,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  primaryActionSubtitle: {
    fontSize: typography.sizes.base,
  },
});
