import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { Sparkles } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { ChatMessage } from '@/stores/chat-store';
import StreamingMessage from './StreamingMessage';

interface AnimatedMessageProps {
  message: ChatMessage;
  isUser: boolean;
  index: number;
  isStreaming?: boolean;
}

const AnimatedView = Animated.createAnimatedComponent(View);

export default function AnimatedMessage({ 
  message, 
  isUser, 
  index, 
  isStreaming = false 
}: AnimatedMessageProps) {
  // Animation values
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);
  const scale = useSharedValue(0.95);
  const sparkleOpacity = useSharedValue(0);
  const sparkleRotation = useSharedValue(0);

  useEffect(() => {
    // Staggered entrance animation
    const delay = index * 100;
    
    opacity.value = withDelay(delay, withTiming(1, { duration: 400 }));
    translateY.value = withDelay(delay, withSpring(0, { damping: 15, stiffness: 150 }));
    scale.value = withDelay(delay, withSpring(1, { damping: 12, stiffness: 200 }));

    // Special sparkle animation for assistant messages
    if (!isUser && !isStreaming) {
      sparkleOpacity.value = withDelay(
        delay + 600,
        withSequence(
          withTiming(1, { duration: 300 }),
          withTiming(0, { duration: 500 })
        )
      );
      sparkleRotation.value = withDelay(
        delay + 600,
        withTiming(360, { duration: 800 })
      );
    }
  }, [index, isUser, isStreaming]);

  // Animated styles
  const messageAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const sparkleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: sparkleOpacity.value,
    transform: [
      { rotate: `${sparkleRotation.value}deg` },
      { scale: interpolate(sparkleOpacity.value, [0, 1, 0], [0.5, 1.2, 0.8]) },
    ],
  }));

  const renderAvatar = () => {
    if (isUser) return null;

    return (
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Sparkles size={16} color={Colors.light.primary} />
        </View>
        
        {/* Animated sparkle effect */}
        <Animated.View style={[styles.sparkleEffect, sparkleAnimatedStyle]}>
          <Sparkles size={12} color={Colors.light.primary} />
        </Animated.View>
      </View>
    );
  };

  const renderMessageContent = () => {
    if (isStreaming) {
      return (
        <StreamingMessage
          content={message.content}
          isUser={isUser}
          isStreaming={true}
          maxPreviewLength={10000}
        />
      );
    }

    return (
      <Text style={[styles.messageText, isUser && styles.userMessageText]}>
        {message.content}
      </Text>
    );
  };

  return (
    <AnimatedView style={[styles.container, messageAnimatedStyle]}>
      <View style={[styles.messageContainer, isUser && styles.userMessageContainer]}>
        {renderAvatar()}
        
        <View style={[
          styles.messageBubble,
          isUser ? styles.userBubble : styles.assistantBubble
        ]}>
          {renderMessageContent()}
          
          {/* Message metadata */}
          <View style={styles.messageMetadata}>
            <Text style={styles.timestamp}>
              {new Date(message.createdAt).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </Text>
            
            {message.totalTokens && (
              <Text style={styles.tokenCount}>
                {message.totalTokens} tokens
              </Text>
            )}
          </View>
        </View>
      </View>
    </AnimatedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: spacing.md,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
    flexDirection: 'row-reverse',
  },
  avatarContainer: {
    position: 'relative',
    marginHorizontal: spacing.sm,
    marginTop: spacing.xs,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sparkleEffect: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  messageBubble: {
    maxWidth: '80%',
    borderRadius: radius.lg,
    padding: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
    borderBottomRightRadius: radius.sm,
  },
  assistantBubble: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border + '40',
    borderBottomLeftRadius: radius.sm,
  },
  messageText: {
    fontSize: typography.sizes.base,
    lineHeight: typography.sizes.base * 1.4,
    color: Colors.light.text,
  },
  userMessageText: {
    color: Colors.light.surface,
  },
  messageMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingTop: spacing.xs,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border + '20',
  },
  timestamp: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    opacity: 0.7,
  },
  tokenCount: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    opacity: 0.5,
    fontFamily: 'monospace',
  },
});
