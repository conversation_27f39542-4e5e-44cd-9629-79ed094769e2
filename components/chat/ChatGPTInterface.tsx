import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { useTimer, useMemoryMonitor, useSafeAsync } from '@/utils/memory-cleanup';
import { useMemoWithTTL, useStableCallback } from '@/utils/memoization-utils';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions,
  Keyboard,
} from 'react-native';
import { Send, Camera, Image as ImageIcon, Sparkles, Menu, X } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useChatStore, ChatMessage, ChatAttachment } from '@/stores/chat-store';
import { useAuthStore } from '@/stores/auth-store';
import { useClientStore } from '@/stores/client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useCommandProcessor } from '@/hooks/useCommandProcessor';
import { useEnhancedHaptics } from '@/hooks/useEnhancedHaptics';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';
import { commonStyles } from '@/styles/commonStyles';
import SmartSuggestions from './SmartSuggestions';
import TypingIndicator from './TypingIndicator';
import ConversationsList from './ConversationsList';
import StreamingMessage from './StreamingMessage';
import QuickActions from './QuickActions';
import CommandSuggestions from './CommandSuggestions';
import AnimatedInput from './AnimatedInput';
import AnimatedMessage from './AnimatedMessage';
import EnhancedTypingIndicator from '../ui/EnhancedTypingIndicator';
import ConfettiEffect from '../ui/ConfettiEffect';
// Modals removed - using ActionSheet instead
// import ImagePickerModal from './ImagePickerModal';
// import ImageContextModal, { ImageContextType } from './ImageContextModal';

// Type from ImageContextModal - keeping for compatibility
type ImageContextType = 'current' | 'desired' | 'result' | 'general';

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface ChatGPTInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
  onClose?: () => void;
  isModal?: boolean;
}

function ChatGPTInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose: _onClose,
  isModal = false,
}: ChatGPTInterfaceProps) {
  // Memory monitoring for performance optimization
  useMemoryMonitor('ChatGPTInterface');

  // Safe async operations
  const { execute: safeExecute } = useSafeAsync();

  // Timer management with automatic cleanup
  const { setTimeout: safeSetTimeout, clearAll: clearAllTimers } = useTimer();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [_isTyping, setIsTyping] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [pendingAttachments, setPendingAttachments] = useState<ChatAttachment[]>([]);
  const [_keyboardHeight, setKeyboardHeight] = useState(0);
  const { user: _user } = useAuthStore();

  // Check if device is tablet
  const { width } = Dimensions.get('window');
  const IS_TABLET = width >= 768;

  // Chat store
  const {
    conversations,
    messages,
    activeConversationId,
    isSending,
    isLoading,
    error: _error,
    streamingMessage,
    typingStatus,
    sendMessage,
    loadMessages,
    loadConversations,
    setActiveConversation,
    createConversation,
    archiveConversation,
    deleteConversation,
    toggleFavorite,
    cleanupStreaming,
    cleanupConversationMemory,
    setTypingStatus,
  } = useChatStore();

  // Context stores
  const { clients: _clients } = useClientStore();
  const { configuration: _configuration } = useSalonConfigStore();

  // Use the working photo analysis hook
  const { takePhoto: _takePhoto, pickImage: _pickImage } = usePhotoAnalysis();

  // Command processor
  const { processCommand, getCommandSuggestions } = useCommandProcessor();

  // Enhanced haptics
  const haptics = useEnhancedHaptics();

  // UI state for enhanced effects
  const [showConfetti, setShowConfetti] = useState(false);

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  // Initialize sidebar for tablets
  useEffect(() => {
    if (IS_TABLET) {
      setShowSidebar(true);
    }
  }, [IS_TABLET]);

  // Enhanced keyboard handling with auto-scroll
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      event => {
        setKeyboardHeight(event.endCoordinates.height);

        // Auto-scroll to bottom when keyboard appears to ensure input is visible
        safeSetTimeout(
          () => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          },
          Platform.OS === 'ios' ? 100 : 300
        );
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, [safeSetTimeout]);

  // Auto-scroll when streaming
  useEffect(() => {
    if (streamingMessage && streamingMessage.conversationId === activeConversationId) {
      // Smooth scroll to bottom while streaming
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }
  }, [streamingMessage?.content, streamingMessage, activeConversationId]);

  // Initialize conversation
  useEffect(() => {
    const initializeChat = async () => {
      // Load conversations first
      await loadConversations();

      // CRITICAL FIX: Use get() to get the updated state after loadConversations
      // This prevents race condition where conversations is empty
      const chatStore = useChatStore.getState();
      const updatedConversations = chatStore.conversations;

      if (propConversationId) {
        setActiveConversation(propConversationId);
        await loadMessages(propConversationId);
      } else if (contextType && contextId) {
        // Check if conversation exists for this context using updated conversations
        const existingConv = updatedConversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          setActiveConversation(existingConv.id);
          await loadMessages(existingConv.id);
        } else {
          // Create new conversation with context
          const title = generateWelcomeTitle();
          const newConv = await createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            setActiveConversation(newConv.id);
          }
        }
      } else if (!activeConversationId && updatedConversations.length > 0) {
        // Select the most recent conversation using updated conversations
        const mostRecent = updatedConversations[0];
        setActiveConversation(mostRecent.id);
        await loadMessages(mostRecent.id);
      }
    };

    initializeChat();
  }, [
    propConversationId,
    contextType,
    contextId,
    contextData,
    loadConversations,
    setActiveConversation,
    loadMessages,
    createConversation,
    activeConversationId,
    generateWelcomeTitle,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
      cleanupStreaming();
      cleanupConversationMemory();
    };
  }, [clearAllTimers, cleanupStreaming, cleanupConversationMemory]);

  // Generate contextual welcome title
  const generateWelcomeTitle = useCallback(() => {
    if (contextType === 'client' && contextData?.name) {
      return `Consulta sobre ${contextData.name}`;
    }
    if (contextType === 'service') {
      return 'Consulta sobre servicio';
    }
    if (contextType === 'formula') {
      return 'Consulta sobre fórmula';
    }
    if (contextType === 'inventory') {
      return 'Consulta de inventario';
    }
    return 'Nueva conversación';
  }, [contextType, contextData?.name]);

  // Generate intelligent conversation starters - memoized with TTL for performance
  const getConversationStarters = useMemoWithTTL(
    () => {
      const starters = [];

      // Context-based starters
      if (contextType === 'client' && contextData) {
        starters.push(`¿Qué técnica recomiendas para ${contextData.name}?`);
      } else {
        // Only 2 most relevant general starters
        starters.push('¿Cómo corrijo un color naranja?');
        starters.push('Fórmula para rubio ceniza nivel 8');
      }

      return starters.slice(0, 2); // Maximum 2 starters
    },
    [contextType, contextData?.name],
    300000
  ); // 5 minutes TTL

  // Handle message send - ChatGPT/Claude style with pending attachments and command processing
  const handleSend = async () => {
    if (!message.trim() || isSending) return;

    // Haptic feedback for send action
    haptics.light();

    const messageToSend = message.trim();
    const attachmentsToSend = [...pendingAttachments]; // Copy current attachments

    // Set typing status based on content
    if (attachmentsToSend.length > 0) {
      setTypingStatus('analyzing');
    } else if (messageToSend.length > 100) {
      setTypingStatus('thinking');
    } else {
      setTypingStatus('writing');
    }

    // Process commands first
    const commandResult = processCommand(messageToSend);

    if (commandResult.isCommand) {
      // Clear input and pending attachments
      setMessage('');
      setPendingAttachments([]);

      // Execute command action if provided
      if (commandResult.action) {
        haptics.success();
        commandResult.action();
        return;
      }

      // If command has a message to send, use that instead
      if (commandResult.shouldSend && commandResult.message) {
        try {
          await safeExecute(() =>
            sendMessage(
              commandResult.message!,
              activeConversationId || undefined,
              attachmentsToSend.length > 0 ? attachmentsToSend : undefined
            )
          );

          // Scroll to bottom after sending
          safeSetTimeout(() => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        } catch (error) {
          logger.error('Error sending command response', 'ChatGPTInterface', error);
          Alert.alert('Error', 'No se pudo procesar el comando. Inténtalo de nuevo.');
        }
      }
      return;
    }

    // Clear input and pending attachments for regular messages
    setMessage('');
    setPendingAttachments([]);

    try {
      // Send regular message with any pending attachments
      await safeExecute(() =>
        sendMessage(
          messageToSend,
          activeConversationId || undefined,
          attachmentsToSend.length > 0 ? attachmentsToSend : undefined
        )
      );

      // Scroll to bottom after sending - using safe timer
      safeSetTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      logger.error('Error sending message', 'ChatGPTInterface', error);
      // Restore attachments if send failed
      setPendingAttachments(attachmentsToSend);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  };

  // Handle typing
  const handleTextChange = (text: string) => {
    setMessage(text);
    setIsTyping(text.length > 0);
  };

  // Handle suggestion selection - memoized for performance
  const handleSuggestionSelect = useStableCallback((suggestion: string) => {
    setMessage(suggestion);
  }, []);

  // Handle conversation selection - with memory cleanup
  const handleSelectConversation = useStableCallback(
    async (id: string) => {
      // Cleanup old conversation memory when switching
      cleanupConversationMemory(id);

      setActiveConversation(id);
      await safeExecute(() => loadMessages(id));
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    },
    [IS_TABLET, cleanupConversationMemory, setActiveConversation, loadMessages]
  );

  // Handle new conversation
  const handleCreateNewConversation = async () => {
    const title = 'Nueva conversación';
    const newConv = await createConversation({ title });
    if (newConv) {
      setActiveConversation(newConv.id);
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  // Direct image upload - ChatGPT/Claude style (no alerts)
  const handleImageUpload = useCallback(
    async (source: 'camera' | 'library') => {
      // Upload image without predefined context - let user specify what they need
      await handleImageSelection(source, 'general', ''); // Empty context, user will provide
    },
    [handleImageSelection]
  );

  // Quick Actions handlers - Simplified
  const _handleQuickAction = async (action: string) => {
    switch (action) {
      case 'new-service':
        setMessage('Quiero iniciar un nuevo servicio de color');
        await handleSend();
        break;
      case 'photo-analysis':
        handleImageUpload('camera');
        break;
      case 'quick-formula':
        // Clear message so user can type their question
        setMessage('');
        // Focus on input
        break;
      default:
        break;
    }
  };

  // Simplified image selection with context
  const handleImageSelection = useCallback(
    async (source: 'camera' | 'library', context: ImageContextType, contextMessage: string) => {
      logger.debug('handleImageSelection', 'ChatGPTInterface', { source, context });

      try {
        if (source === 'camera') {
          logger.debug('Direct camera implementation', 'ChatGPTInterface');

          // Request permissions directly
          const { status } = await ImagePicker.requestCameraPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
            return;
          }

          logger.debug('Launching camera directly', 'ChatGPTInterface');

          // Use the most minimal configuration possible
          const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ['images'],
            quality: 0.5,
            base64: false,
          });

          logger.debug('Direct camera result', 'ChatGPTInterface', result);

          if (!result.canceled && result.assets && result.assets[0]) {
            const asset = result.assets[0];
            // Process image directly with context message
            await processImageWithContext(asset, contextMessage);
          }
        } else {
          logger.debug('Direct gallery implementation', 'ChatGPTInterface');

          // Request permissions directly
          const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert(
              'Permisos necesarios',
              'Se necesitan permisos de galería para seleccionar fotos'
            );
            return;
          }

          logger.debug('Launching gallery directly', 'ChatGPTInterface');

          // Allow multiple images for better analysis (max 3)
          const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            quality: 0.5,
            base64: false,
            allowsMultipleSelection: true,
            selectionLimit: 3,
          });

          logger.debug('Direct gallery result', 'ChatGPTInterface', result);

          if (!result.canceled && result.assets && result.assets.length > 0) {
            // Process all selected images
            await processMultipleImagesWithContext(result.assets, contextMessage);
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('Error in direct image picker', 'ChatGPTInterface', error);
        if (__DEV__ && error instanceof Error) {
          logger.error('Error details', 'ChatGPTInterface', {
            message: error.message,
            stack: error.stack,
            name: error.name,
          });
        }
        Alert.alert('Error', `Error al seleccionar imagen: ${errorMessage}`);
      }
    },
    [processImageWithContext]
  );

  // Process multiple images and attach to current message - ChatGPT/Claude style
  const processMultipleImagesWithContext = useCallback(
    async (assets: { uri: string; fileSize?: number }[], contextMessage: string) => {
      try {
        const attachments: ChatAttachment[] = [];

        // Process each image
        for (const asset of assets) {
          // Show size info only in dev
          const originalSizeMB = asset.fileSize
            ? (asset.fileSize / (1024 * 1024)).toFixed(1)
            : 'Desconocido';
          if (__DEV__) logger.info(`Processing image: ${originalSizeMB}MB original size`);

          // Compress image
          const compressedBase64 = await ImageProcessor.compressForUpload(asset.uri, true, 'chat');
          const imageDataUrl = `data:image/jpeg;base64,${compressedBase64}`;

          // Calculate compressed size
          const compressedSizeKB = Math.round(imageDataUrl.length / 1024);
          if (__DEV__) logger.info(`Compressed to: ${compressedSizeKB}KB`);

          // Add to attachments
          attachments.push({
            type: 'image' as const,
            url: imageDataUrl,
            mimeType: 'image/jpeg',
          });
        }

        // Instead of sending immediately, store the attachments and let user type their message
        // This mimics ChatGPT/Claude behavior where images are attached but user types what they want
        if (contextMessage) {
          // If there's a predefined context (legacy), send immediately
          await sendMessage(contextMessage, activeConversationId || undefined, attachments);
        } else {
          // ChatGPT/Claude style: Just show the images are attached and wait for user message
          // Limit total attachments to 3 to prevent overwhelming the interface
          setPendingAttachments(prev => {
            const combined = [...prev, ...attachments];
            if (combined.length > 3) {
              Alert.alert(
                'Límite de imágenes',
                'Máximo 3 imágenes por consulta. Se han seleccionado las primeras 3.',
                [{ text: 'OK' }]
              );
              return combined.slice(0, 3);
            }
            return combined;
          });
          // No alert needed - the visual preview is enough feedback
        }

        // Scroll to bottom after processing - using safe timer
        safeSetTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        logger.error('Error processing images', 'ChatGPTInterface', error);
        Alert.alert('Error', 'No se pudo procesar las imágenes. Inténtalo de nuevo.');
      }
    },
    [activeConversationId, sendMessage, safeSetTimeout]
  );

  // Process single image and attach to current message - ChatGPT/Claude style
  const processImageWithContext = useCallback(
    async (asset: { uri: string; fileSize?: number }, contextMessage: string) => {
      await processMultipleImagesWithContext([asset], contextMessage);
    },
    [processMultipleImagesWithContext]
  );

  // Deprecated functions - removed in favor of ActionSheet
  // const handleImageUpload = async (asset: any) => { ... }
  // const handleImageContextSelect = async (context, message) => { ... }

  // Render welcome screen - Ultra minimal like Claude.ai
  const renderWelcomeScreen = () => {
    const starters = getConversationStarters;

    return (
      <ScrollView style={styles.welcomeContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.welcomeContent}>
          <View style={styles.welcomeLogo}>
            <Sparkles size={32} color={Colors.light.primary} />
          </View>
          <Text style={styles.welcomeTitle}>Salonier Assistant</Text>
          <Text style={styles.welcomeSubtitle}>¿En qué puedo ayudarte hoy?</Text>

          {/* Quick Actions - Main feature */}
          <QuickActions
            onActionPress={(message) => {
              setMessage(message);
              handleSend();
            }}
            onImageCapture={() => handleImageUpload('camera')}
            onCelebration={() => {
              setShowConfetti(true);
              haptics.celebration();
            }}
          />

          {/* Subtle suggestion chips - Keep as fallback */}
          {starters.length > 0 && (
            <View style={styles.suggestionChips}>
              {starters.map((starter, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionChip}
                  onPress={() => {
                    setMessage(starter);
                    handleSend();
                  }}
                  activeOpacity={0.6}
                >
                  <Text style={styles.suggestionText}>{starter}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  // Render message bubble
  const renderMessage = (msg: ChatMessage, index: number) => {
    const isUser = msg.role === 'user';

    return (
      <View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        {!isUser && (
          <View style={styles.assistantAvatar}>
            <Sparkles size={14} color={Colors.light.primary} />
          </View>
        )}

        <View style={[styles.messageBubble, isUser ? styles.userBubble : styles.assistantBubble]}>
          {/* Render attachments if any */}
          {msg.attachments && msg.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              {msg.attachments.map((attachment, attachIndex) => (
                <View key={attachIndex} style={styles.attachmentContainer}>
                  {attachment.type === 'image' && (
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Render message text with StreamingMessage */}
          {msg.content && (
            <StreamingMessage
              content={msg.content}
              isUser={isUser}
              isStreaming={!isUser && index === currentMessages.length - 1 && isSending}
              maxPreviewLength={300}
            />
          )}
        </View>
      </View>
    );
  };

  // Calculate proper keyboard offset with enhanced logic
  const getKeyboardVerticalOffset = () => {
    if (isModal) {
      return 0;
    }

    // For iOS, we need to account for:
    // - Status bar height (insets.top)
    // - Navigation header height (approximately 44-50 points)
    // - Safe area bottom (insets.bottom)
    if (Platform.OS === 'ios') {
      // More precise calculation for iOS
      return insets.top + 44 - insets.bottom;
    }

    // For Android, usually no offset needed with 'height' behavior
    return 0;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      enabled={true}
    >
      {/* Sidebar for tablets or when shown */}
      {(IS_TABLET || showSidebar) && (
        <>
          {!IS_TABLET && showSidebar && (
            <TouchableOpacity
              style={styles.sidebarBackdrop}
              activeOpacity={1}
              onPress={() => setShowSidebar(false)}
            />
          )}
          <View style={[styles.sidebar, !IS_TABLET && styles.sidebarOverlay]}>
            <ConversationsList
              conversations={conversations}
              activeConversationId={activeConversationId}
              isLoading={isLoading}
              onSelectConversation={handleSelectConversation}
              onArchiveConversation={archiveConversation}
              onDeleteConversation={deleteConversation}
              onNewConversation={handleCreateNewConversation}
              onToggleFavorite={toggleFavorite}
            />
          </View>
        </>
      )}

      {/* Main chat area */}
      <View
        style={[styles.mainContent, (IS_TABLET || showSidebar) && styles.mainContentWithSidebar]}
      >
        {/* Header - Ultra Minimal */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={toggleSidebar} style={styles.menuButton}>
                <Menu size={20} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            )}
            <Text style={styles.headerTitle}>Salonier</Text>
            <View style={commonStyles.width20} />
          </View>
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {currentMessages.length === 0 ? (
            renderWelcomeScreen()
          ) : (
            <>
              {currentMessages.map((msg, index) => renderMessage(msg, index))}
              {/* Render streaming message if active */}
              {streamingMessage && streamingMessage.conversationId === activeConversationId && (
                <View style={[styles.messageContainer, styles.assistantMessageContainer]}>
                  <View style={styles.assistantAvatar}>
                    <Sparkles size={14} color={Colors.light.primary} />
                  </View>
                  <View style={[styles.messageBubble, styles.assistantBubble]}>
                    <StreamingMessage
                      content={streamingMessage.content}
                      isUser={false}
                      isStreaming={true}
                      maxPreviewLength={10000} // Don't truncate streaming messages
                    />
                  </View>
                </View>
              )}
              <EnhancedTypingIndicator
                visible={isSending && !streamingMessage}
                status={typingStatus}
              />
            </>
          )}
        </ScrollView>

        {/* Input Container with Command and Smart Suggestions */}
        <View style={styles.bottomContainer}>
          {/* Command Suggestions - Show when typing commands */}
          <CommandSuggestions
            input={message}
            onSuggestionSelect={(command) => {
              setMessage(command + ' ');
            }}
          />

          {/* Smart Suggestions - Above input for better visibility */}
          {message.length > 0 && !message.startsWith('/') && (
            <SmartSuggestions
              input={message}
              onSuggestionSelect={handleSuggestionSelect}
              contextType={contextType}
              contextData={contextData}
            />
          )}

          {/* Pending attachments preview - ChatGPT/Claude style */}
          {pendingAttachments.length > 0 && (
            <View style={styles.pendingAttachmentsContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.pendingAttachmentsScroll}
                contentContainerStyle={styles.pendingAttachmentsContent}
              >
                {pendingAttachments.map((attachment, index) => (
                  <View key={index} style={styles.pendingImageWrapper}>
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.pendingImagePreview}
                      resizeMode="cover"
                    />
                    {attachment.uploadStatus === 'uploading' && (
                      <View style={styles.uploadingOverlay}>
                        <ActivityIndicator size="small" color={Colors.light.surface} />
                      </View>
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        const newAttachments = pendingAttachments.filter((_, i) => i !== index);
                        setPendingAttachments(newAttachments);
                      }}
                      style={styles.removePendingImageButton}
                    >
                      <X size={12} color={Colors.light.surface} />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
              <Text style={styles.pendingAttachmentsHint}>
                {pendingAttachments.length} de 3 imagen{pendingAttachments.length > 1 ? 'es' : ''}{' '}
                lista{pendingAttachments.length > 1 ? 's' : ''} • Escribe qué necesitas que analice
              </Text>
            </View>
          )}

          {/* Animated Input */}
          <View style={styles.inputContainer}>
            <AnimatedInput
              value={message}
              onChangeText={handleTextChange}
              onSend={handleSend}
              onImageCapture={() => handleImageUpload('camera')}
              onImagePicker={() => handleImageUpload('library')}
              placeholder={
                pendingAttachments.length > 0
                  ? 'Describe qué necesitas que analice...'
                  : 'Pregúntame lo que necesites...'
              }
              isSending={isSending}
              disabled={pendingAttachments.length >= 3}
            />
          </View>
        </View>
      </View>

      {/* Confetti Effect */}
      <ConfettiEffect
        visible={showConfetti}
        onComplete={() => setShowConfetti(false)}
        intensity="medium"
      />

      {/* Modals removed - using ActionSheet instead */}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },

  // Sidebar
  sidebar: {
    width: 300,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  sidebarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  sidebarBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.backdropColor,
    zIndex: 999,
  },

  // Main content
  mainContent: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainContentWithSidebar: {
    marginLeft: 0, // Will be handled by flexDirection: 'row'
  },

  // Header - Minimal
  header: {
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '20',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },

  // Welcome Screen - Modern and clean
  welcomeContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  welcomeContent: {
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  welcomeLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  welcomeTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xl,
    textAlign: 'center',
    lineHeight: typography.sizes.lg * 1.3,
  },

  // Suggestion Chips - Subtle
  suggestionChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: spacing.sm,
    marginTop: spacing.md,
  },
  suggestionChip: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  suggestionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },

  // Messages
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    flexGrow: 1, // Asegura que el contenido crezca
  },
  messageContainer: {
    marginBottom: spacing.md,
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%', // Usar todo el ancho
    paddingHorizontal: spacing.xs, // Pequeño padding para evitar que toque los bordes
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  assistantMessageContainer: {
    justifyContent: 'flex-start',
    flex: 1, // Permitir que el mensaje del asistente use todo el espacio disponible
  },

  // Assistant Avatar
  assistantAvatar: {
    width: 28, // Reducido para dar más espacio al texto
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.xs, // Reducido margen
    marginTop: spacing.xs,
    flexShrink: 0, // Evita que se comprima
  },

  // Message Bubbles - Modern ChatGPT style
  messageBubble: {
    maxWidth: '85%',
    borderRadius: radius.lg,
    padding: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
    marginLeft: spacing.xl,
    alignSelf: 'flex-end',
  },
  assistantBubble: {
    backgroundColor: Colors.light.surface,
    marginRight: spacing.xl,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: Colors.light.border + '30',
  },
  // messageText: {
  //   fontSize: typography.sizes.lg,
  //   lineHeight: typography.sizes.lg * 1.4,
  // },
  // userMessageText: {
  //   color: 'white',
  // },
  // assistantMessageText: {
  //   color: Colors.light.text,
  // },

  // Attachments
  attachmentsContainer: {
    marginBottom: spacing.sm,
  },
  attachmentContainer: {
    marginBottom: spacing.xs,
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: radius.md,
    backgroundColor: Colors.light.border,
  },

  // Bottom Container for suggestions and input - Fixed positioning
  bottomContainer: {
    backgroundColor: Colors.light.background,
    // Ensure the container is always at the bottom
    flexShrink: 0, // Prevent shrinking when keyboard appears
    minHeight: 'auto', // Allow natural height calculation
  },

  // Input - Minimal & Clean with better visibility
  inputContainer: {
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    // Add subtle shadow for better definition
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2, // Android shadow
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    borderWidth: 1,
    borderColor: Colors.light.border + '60',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 56,
    maxHeight: 120,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  attachButton: {
    padding: spacing.sm,
    marginRight: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: 'transparent',
    // Better touch feedback
    opacity: 1,
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    maxHeight: 80,
    lineHeight: typography.sizes.base * 1.4,
    textAlignVertical: 'center',
    fontWeight: typography.weights.normal,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sendButtonActive: {
    backgroundColor: Colors.light.primary,
    transform: [{ scale: 1 }],
  },
  sendButtonInactive: {
    backgroundColor: Colors.light.border + '80',
  },

  // Pending attachments styles - ChatGPT/Claude style
  pendingAttachmentsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.sm,
    backgroundColor: Colors.light.background,
  },
  pendingAttachmentsScroll: {
    marginBottom: spacing.xs,
  },
  pendingAttachmentsContent: {
    paddingRight: spacing.sm,
  },
  pendingImageWrapper: {
    position: 'relative',
    marginRight: spacing.sm,
    borderRadius: radius.md,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  pendingImagePreview: {
    width: 80,
    height: 80,
    borderRadius: radius.md,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.text + '60',
    borderRadius: radius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePendingImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.text + '80',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pendingAttachmentsHint: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

// Export memoized component for performance optimization
export default memo(ChatGPTInterface, (prevProps, nextProps) => {
  // Custom comparison for performance
  return (
    prevProps.conversationId === nextProps.conversationId &&
    prevProps.contextType === nextProps.contextType &&
    prevProps.contextId === nextProps.contextId &&
    prevProps.isModal === nextProps.isModal &&
    JSON.stringify(prevProps.contextData) === JSON.stringify(nextProps.contextData)
  );
});
