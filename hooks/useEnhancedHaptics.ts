import { useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { useWhimsyStore } from '@/stores/whimsy-store';

export type HapticPattern = 
  | 'light'
  | 'medium' 
  | 'heavy'
  | 'success'
  | 'warning'
  | 'error'
  | 'selection'
  | 'notification'
  | 'heartbeat'
  | 'bounce'
  | 'celebration';

export function useEnhancedHaptics() {
  const { enableHapticFeedback } = useWhimsyStore();

  const triggerHaptic = useCallback(async (pattern: HapticPattern) => {
    if (!enableHapticFeedback || Platform.OS !== 'ios') return;

    try {
      switch (pattern) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
          
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
          
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
          
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
          
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
          
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
          
        case 'selection':
          await Haptics.selectionAsync();
          break;
          
        case 'notification':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
          
        case 'heartbeat':
          // Custom pattern: two quick pulses
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setTimeout(async () => {
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }, 100);
          break;
          
        case 'bounce':
          // Custom pattern: medium then light
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          setTimeout(async () => {
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }, 150);
          break;
          
        case 'celebration':
          // Custom pattern: success followed by light impacts
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          setTimeout(async () => {
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setTimeout(async () => {
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 100);
          }, 200);
          break;
          
        default:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      // Silently fail if haptics are not available
      console.warn('Haptic feedback failed:', error);
    }
  }, [enableHapticFeedback]);

  // Convenience methods for common patterns
  const success = useCallback(() => triggerHaptic('success'), [triggerHaptic]);
  const error = useCallback(() => triggerHaptic('error'), [triggerHaptic]);
  const warning = useCallback(() => triggerHaptic('warning'), [triggerHaptic]);
  const selection = useCallback(() => triggerHaptic('selection'), [triggerHaptic]);
  const light = useCallback(() => triggerHaptic('light'), [triggerHaptic]);
  const medium = useCallback(() => triggerHaptic('medium'), [triggerHaptic]);
  const heavy = useCallback(() => triggerHaptic('heavy'), [triggerHaptic]);
  const celebration = useCallback(() => triggerHaptic('celebration'), [triggerHaptic]);
  const heartbeat = useCallback(() => triggerHaptic('heartbeat'), [triggerHaptic]);
  const bounce = useCallback(() => triggerHaptic('bounce'), [triggerHaptic]);

  return {
    triggerHaptic,
    success,
    error,
    warning,
    selection,
    light,
    medium,
    heavy,
    celebration,
    heartbeat,
    bounce,
  };
}
