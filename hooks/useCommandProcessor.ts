import { useCallback } from 'react';
import { router } from 'expo-router';
import { Alert } from 'react-native';
import { useClientStore } from '@/stores/client-store';
import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';

interface CommandResult {
  isCommand: boolean;
  shouldSend: boolean;
  message?: string;
  action?: () => void;
}

interface Command {
  pattern: RegExp;
  description: string;
  handler: (matches: RegExpMatchArray) => CommandResult;
}

export function useCommandProcessor() {
  const { clients, loadClients } = useClientStore();
  const { products, loadProducts } = useInventoryStore();
  const { configuration } = useSalonConfigStore();

  const commands: Command[] = [
    // Nuevo servicio
    {
      pattern: /^\/nuevo-servicio(?:\s+(.+))?$/i,
      description: 'Iniciar un nuevo servicio de coloración',
      handler: (matches) => {
        const clientName = matches[1]?.trim();
        if (clientName) {
          // Buscar cliente por nombre
          const client = clients.find(c => 
            c.name.toLowerCase().includes(clientName.toLowerCase())
          );
          
          if (client) {
            return {
              isCommand: true,
              shouldSend: false,
              action: () => router.push(`/service/new?clientId=${client.id}`)
            };
          } else {
            return {
              isCommand: true,
              shouldSend: true,
              message: `No encontré un cliente llamado "${clientName}". ¿Quieres que te ayude a crear un nuevo cliente o buscar entre los existentes?`
            };
          }
        } else {
          return {
            isCommand: true,
            shouldSend: false,
            action: () => router.push('/service/client-selection')
          };
        }
      }
    },

    // Buscar cliente
    {
      pattern: /^\/cliente(?:\s+(.+))?$/i,
      description: 'Buscar información de un cliente',
      handler: (matches) => {
        const clientName = matches[1]?.trim();
        if (clientName) {
          const matchingClients = clients.filter(c => 
            c.name.toLowerCase().includes(clientName.toLowerCase())
          );
          
          if (matchingClients.length === 1) {
            const client = matchingClients[0];
            return {
              isCommand: true,
              shouldSend: true,
              message: `Información del cliente ${client.name}:\n\n📧 Email: ${client.email || 'No especificado'}\n📱 Teléfono: ${client.phone || 'No especificado'}\n🎂 Fecha de nacimiento: ${client.dateOfBirth ? new Date(client.dateOfBirth).toLocaleDateString() : 'No especificada'}\n\n¿Qué te gustaría saber sobre este cliente?`
            };
          } else if (matchingClients.length > 1) {
            const clientList = matchingClients.map(c => `• ${c.name}`).join('\n');
            return {
              isCommand: true,
              shouldSend: true,
              message: `Encontré varios clientes con ese nombre:\n\n${clientList}\n\n¿Podrías ser más específico?`
            };
          } else {
            return {
              isCommand: true,
              shouldSend: true,
              message: `No encontré ningún cliente llamado "${clientName}". ¿Quieres que te ayude a buscar entre todos los clientes o crear uno nuevo?`
            };
          }
        } else {
          return {
            isCommand: true,
            shouldSend: false,
            action: () => router.push('/clients')
          };
        }
      }
    },

    // Consultar inventario
    {
      pattern: /^\/inventario(?:\s+(.+))?$/i,
      description: 'Consultar productos en inventario',
      handler: (matches) => {
        const productQuery = matches[1]?.trim();
        if (productQuery) {
          const matchingProducts = products.filter(p => 
            p.name.toLowerCase().includes(productQuery.toLowerCase()) ||
            p.brand.toLowerCase().includes(productQuery.toLowerCase()) ||
            p.line?.toLowerCase().includes(productQuery.toLowerCase())
          );
          
          if (matchingProducts.length > 0) {
            const productList = matchingProducts.slice(0, 5).map(p => 
              `• ${p.brand} ${p.line ? p.line + ' ' : ''}${p.name} - Stock: ${p.currentStock || 0} ${p.unit}`
            ).join('\n');
            
            const moreText = matchingProducts.length > 5 ? `\n\n... y ${matchingProducts.length - 5} productos más.` : '';
            
            return {
              isCommand: true,
              shouldSend: true,
              message: `Productos encontrados para "${productQuery}":\n\n${productList}${moreText}\n\n¿Necesitas más detalles sobre algún producto específico?`
            };
          } else {
            return {
              isCommand: true,
              shouldSend: true,
              message: `No encontré productos que coincidan con "${productQuery}". ¿Quieres que te muestre todo el inventario disponible?`
            };
          }
        } else {
          return {
            isCommand: true,
            shouldSend: false,
            action: () => router.push('/inventory')
          };
        }
      }
    },

    // Reportes
    {
      pattern: /^\/reportes?(?:\s+(.+))?$/i,
      description: 'Ver reportes y estadísticas',
      handler: (matches) => {
        const reportType = matches[1]?.trim().toLowerCase();
        if (reportType) {
          return {
            isCommand: true,
            shouldSend: true,
            message: `Te ayudo con reportes de ${reportType}. ¿Qué período te interesa? ¿Hoy, esta semana, este mes?`
          };
        } else {
          return {
            isCommand: true,
            shouldSend: false,
            action: () => router.push('/services')
          };
        }
      }
    },

    // Fórmula rápida
    {
      pattern: /^\/formula(?:\s+(.+))?$/i,
      description: 'Crear una fórmula personalizada',
      handler: (matches) => {
        const description = matches[1]?.trim();
        if (description) {
          return {
            isCommand: true,
            shouldSend: true,
            message: `Perfecto, voy a ayudarte a crear una fórmula para: "${description}"\n\n¿Podrías darme más detalles?\n• Color actual del cabello\n• Color deseado\n• Tipo de cabello\n• Tratamientos previos`
          };
        } else {
          return {
            isCommand: true,
            shouldSend: true,
            message: `¿Para qué tipo de coloración necesitas la fórmula? Por ejemplo:\n• Rubio ceniza nivel 8\n• Castaño chocolate\n• Mechas balayage\n• Corrección de color`
          };
        }
      }
    },

    // Ayuda
    {
      pattern: /^\/ayuda?$/i,
      description: 'Mostrar comandos disponibles',
      handler: () => {
        const commandList = commands.map(cmd => 
          `• ${cmd.pattern.source.replace(/\^|\$|\(\?\:|\)|\?\$|\|/g, '').replace(/\\\//g, '/')} - ${cmd.description}`
        ).join('\n');
        
        return {
          isCommand: true,
          shouldSend: true,
          message: `Comandos disponibles:\n\n${commandList}\n\nTambién puedes escribir en lenguaje natural y te ayudaré con lo que necesites.`
        };
      }
    }
  ];

  const processCommand = useCallback((input: string): CommandResult => {
    const trimmedInput = input.trim();
    
    // Verificar si es un comando
    for (const command of commands) {
      const matches = trimmedInput.match(command.pattern);
      if (matches) {
        try {
          return command.handler(matches);
        } catch (error) {
          console.error('Error processing command:', error);
          return {
            isCommand: true,
            shouldSend: true,
            message: 'Hubo un error procesando el comando. Por favor, inténtalo de nuevo.'
          };
        }
      }
    }
    
    // No es un comando
    return {
      isCommand: false,
      shouldSend: true
    };
  }, [clients, products, configuration]);

  const getCommandSuggestions = useCallback((input: string): string[] => {
    if (!input.startsWith('/')) return [];
    
    const suggestions = commands
      .filter(cmd => {
        const cmdName = cmd.pattern.source.match(/\^\\\/(\w+)/)?.[1];
        return cmdName?.startsWith(input.slice(1).toLowerCase());
      })
      .map(cmd => {
        const cmdName = cmd.pattern.source.match(/\^\\\/(\w+)/)?.[1];
        return `/${cmdName}`;
      });
    
    return suggestions.slice(0, 3);
  }, []);

  return {
    processCommand,
    getCommandSuggestions,
    commands: commands.map(cmd => ({
      pattern: cmd.pattern.source,
      description: cmd.description
    }))
  };
}
